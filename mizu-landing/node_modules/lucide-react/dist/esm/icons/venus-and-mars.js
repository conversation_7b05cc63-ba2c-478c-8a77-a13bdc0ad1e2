/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  ["path", { d: "M10 20h4", key: "ni2waw" }],
  ["path", { d: "M12 16v6", key: "c8a4gj" }],
  ["path", { d: "M17 2h4v4", key: "vhe59" }],
  ["path", { d: "m21 2-5.46 5.46", key: "19kypf" }],
  ["circle", { cx: "12", cy: "11", r: "5", key: "16gxyc" }]
];
const VenusAndMars = createLucideIcon("venus-and-mars", __iconNode);

export { __iconNode, VenusAndMars as default };
//# sourceMappingURL=venus-and-mars.js.map
