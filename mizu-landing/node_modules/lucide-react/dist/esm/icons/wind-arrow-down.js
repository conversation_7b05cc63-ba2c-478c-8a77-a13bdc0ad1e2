/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  ["path", { d: "M10 2v8", key: "d4bbey" }],
  ["path", { d: "M12.8 21.6A2 2 0 1 0 14 18H2", key: "19kp1d" }],
  ["path", { d: "M17.5 10a2.5 2.5 0 1 1 2 4H2", key: "19kpjc" }],
  ["path", { d: "m6 6 4 4 4-4", key: "k13n16" }]
];
const WindArrowDown = createLucideIcon("wind-arrow-down", __iconNode);

export { __iconNode, WindArrowDown as default };
//# sourceMappingURL=wind-arrow-down.js.map
