@import "tailwindcss";

:root {
  --font-roboto: var(--font-roboto);
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f8fa;
}

::-webkit-scrollbar-thumb {
  background: #069494;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #004f6e;
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Focus styles for accessibility */
*:focus-visible {
  outline: 2px solid #069494;
  outline-offset: 2px;
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}
