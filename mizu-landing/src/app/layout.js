import { <PERSON><PERSON> } from "next/font/google";
import "./globals.css";

const roboto = Roboto({
  weight: ['400', '500', '700'],
  subsets: ["latin"],
  display: 'swap',
  variable: '--font-roboto',
});

export const metadata = {
  title: "MizuFlow - Automate Complex Financial Workflows with AI",
  description: "Transform your financial operations with intelligent automation. MizuFlow delivers end-to-end automation, accounting, and financial services to streamline your business processes and drive growth.",
  keywords: "financial automation, AI accounting, workflow automation, financial services, business intelligence",
  authors: [{ name: "<PERSON><PERSON><PERSON><PERSON>" }],
  viewport: "width=device-width, initial-scale=1",
};

export default function RootLayout({ children }) {
  return (
    <html lang="en" className={roboto.variable}>
      <body className="font-sans antialiased bg-white text-surface-dark">
        {children}
      </body>
    </html>
  );
}
