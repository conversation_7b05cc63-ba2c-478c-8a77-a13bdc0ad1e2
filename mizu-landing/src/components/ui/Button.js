import { forwardRef } from 'react';

const Button = forwardRef(({ 
  children, 
  variant = 'default', 
  size = 'default', 
  className = '', 
  disabled = false,
  ...props 
}, ref) => {
  const baseStyles = 'inline-flex items-center justify-center rounded-md font-medium transition-all duration-300 ease-out focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-secondary focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50';
  
  const variants = {
    default: 'bg-primary text-white hover:bg-ocean-900 hover:scale-105 active:scale-95',
    secondary: 'bg-secondary text-white hover:bg-primary hover:scale-105 active:scale-95',
    outline: 'border-2 border-primary text-primary bg-transparent hover:bg-primary hover:text-white hover:scale-105 active:scale-95',
    ghost: 'text-primary hover:bg-surface hover:text-primary hover:scale-105 active:scale-95',
  };
  
  const sizes = {
    sm: 'h-9 px-3 text-sm',
    default: 'h-12 px-6 py-3',
    lg: 'h-14 px-8 py-4 text-lg',
  };
  
  const variantStyles = variants[variant] || variants.default;
  const sizeStyles = sizes[size] || sizes.default;
  
  return (
    <button
      ref={ref}
      className={`${baseStyles} ${variantStyles} ${sizeStyles} ${className}`}
      disabled={disabled}
      {...props}
    >
      {children}
    </button>
  );
});

Button.displayName = 'Button';

export default Button;
