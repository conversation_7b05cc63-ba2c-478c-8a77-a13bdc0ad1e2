'use client';

import { motion } from 'framer-motion';
import { useInView } from 'framer-motion';
import { useRef } from 'react';
import { Zap, Shield, TrendingUp, Clock, Brain, Target } from 'lucide-react';
import { Card, CardContent } from '../ui/Card';

const benefits = [
  {
    icon: Zap,
    title: 'Intelligent Automation',
    description: 'Leverage AI and machine learning to automate repetitive financial tasks, reducing manual effort and minimizing errors.',
  },
  {
    icon: Brain,
    title: 'Data-Driven Insights',
    description: 'Transform raw financial data into actionable business intelligence to make informed strategic decisions.',
  },
  {
    icon: Shield,
    title: 'Enhanced Accuracy',
    description: 'Improve data accuracy and compliance with automated validation and reconciliation processes.',
  },
  {
    icon: Clock,
    title: 'Time Savings',
    description: 'Reduce processing time by up to 80% with automated workflows and intelligent document processing.',
  },
  {
    icon: TrendingUp,
    title: 'Scalable Growth',
    description: 'Scale your financial operations seamlessly as your business grows without proportional staff increases.',
  },
  {
    icon: Target,
    title: 'Strategic Focus',
    description: 'Free up your team to focus on strategic initiatives while automation handles routine tasks.',
  },
];

export default function Benefits() {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, margin: '-100px' });

  return (
    <section id="features" className="py-24 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          ref={ref}
          className="text-center mb-16"
          initial={{ opacity: 0, y: 30 }}
          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
          transition={{ duration: 0.8 }}
        >
          <h2 className="text-3xl lg:text-4xl font-bold text-surface-dark mb-4">
            Why Choose MizuFlow
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            We combine cutting-edge technology with financial expertise to deliver 
            solutions that drive efficiency and growth.
          </p>
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {benefits.map((benefit, index) => {
            const Icon = benefit.icon;
            
            return (
              <motion.div
                key={benefit.title}
                initial={{ opacity: 0, y: 30 }}
                animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
                transition={{ 
                  duration: 0.6, 
                  delay: index * 0.1,
                  ease: 'easeOut'
                }}
                whileHover={{ 
                  scale: 1.05,
                  transition: { duration: 0.2 }
                }}
              >
                <Card className="h-full hover:shadow-lg transition-all duration-300 border-0 shadow-md">
                  <CardContent className="p-8 text-center">
                    <motion.div
                      className="inline-flex items-center justify-center w-16 h-16 bg-gradient-ocean-teal rounded-full mb-6"
                      whileHover={{ rotate: 360 }}
                      transition={{ duration: 0.6 }}
                    >
                      <Icon className="h-8 w-8 text-white" />
                    </motion.div>
                    
                    <h3 className="text-xl font-semibold text-surface-dark mb-4">
                      {benefit.title}
                    </h3>
                    
                    <p className="text-gray-600 leading-relaxed">
                      {benefit.description}
                    </p>
                  </CardContent>
                </Card>
              </motion.div>
            );
          })}
        </div>
      </div>
    </section>
  );
}
