/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    './src/pages/**/*.{js,ts,jsx,tsx,mdx}',
    './src/components/**/*.{js,ts,jsx,tsx,mdx}',
    './src/app/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  theme: {
    extend: {
      colors: {
        'ocean-900': '#011720',
        'ocean-600': '#004F6E',
        'teal-500': '#069494',
        'mist-100': '#F1F8FA',
        // Semantic color tokens
        primary: '#004F6E', // ocean-600
        secondary: '#069494', // teal-500
        surface: '#F1F8FA', // mist-100
        'surface-dark': '#011720', // ocean-900
      },
      fontFamily: {
        sans: ['Roboto', 'system-ui', 'sans-serif'],
      },
      spacing: {
        // 8-point spacing system
        '1': '0.125rem', // 2px
        '2': '0.25rem',  // 4px
        '3': '0.375rem', // 6px
        '4': '0.5rem',   // 8px
        '5': '0.625rem', // 10px
        '6': '0.75rem',  // 12px
        '8': '1rem',     // 16px
        '10': '1.25rem', // 20px
        '12': '1.5rem',  // 24px
        '16': '2rem',    // 32px
        '20': '2.5rem',  // 40px
        '24': '3rem',    // 48px
        '32': '4rem',    // 64px
        '40': '5rem',    // 80px
        '48': '6rem',    // 96px
        '56': '7rem',    // 112px
        '64': '8rem',    // 128px
      },
      fontSize: {
        'hero': ['5.5rem', { lineHeight: '1.1', fontWeight: '700' }],
        'xl': ['1.25rem', { lineHeight: '1.5', fontWeight: '500' }],
      },
      backgroundImage: {
        'gradient-ocean-teal': 'linear-gradient(to bottom right, #004F6E, #069494 60%)',
      },
      animation: {
        'float': 'float 6s ease-in-out infinite',
        'slide': 'slide 30s linear infinite',
      },
      keyframes: {
        float: {
          '0%, 100%': { transform: 'translateY(0px)' },
          '50%': { transform: 'translateY(-20px)' },
        },
        slide: {
          '0%': { transform: 'translateX(0)' },
          '100%': { transform: 'translateX(-100%)' },
        },
      },
    },
  },
  plugins: [],
}
